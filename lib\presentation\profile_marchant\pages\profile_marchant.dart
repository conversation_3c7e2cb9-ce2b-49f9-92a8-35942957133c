import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/core/services/calculate_profile_progress.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';
import 'package:wesell/presentation/settings/pages/settings_page.dart';

class ProfileMarchant extends StatefulWidget {
  const ProfileMarchant({super.key});

  @override
  State<ProfileMarchant> createState() => _ProfileMarchantState();
}

class _ProfileMarchantState extends State<ProfileMarchant> {
  ProfileMarchantModel? profiledata;
  late final List<MenuItem> menuItems;

   @override
  void initState() {
    super.initState();
    // Trigger profile loading when the widget initializes
    if(mounted){
      context.read<ProfileMarchantBloc>().add(GetProfileEvent());
    }

    menuItems = [
      MenuItem(title: AppStrings.companyDetails,onTap: companyDetails),
      MenuItem(title: AppStrings.industryDetails, onTap: industryDetails),
      MenuItem(title: AppStrings.companyLocation, onTap: companyLocations),
      MenuItem(title: AppStrings.digitalInformation, onTap: digitalInformation),
      MenuItem(title: AppStrings.legalDocuments, onTap: legalDocuments),
      MenuItem(title: AppStrings.subscriptionPlan, onTap: subscriptionPlan),
      MenuItem(title: AppStrings.inAppCredit, onTap: inAppCredit),
      MenuItem(title: AppStrings.bankDetails,),
    ];
  }

  void companyDetails(){
    AppRoutes.pushScreen(context, AppRoutes.companyDetails,arguments: profiledata);
  }

  void industryDetails(){
    AppRoutes.pushScreen(context, AppRoutes.industryDetails,arguments: profiledata);
  }

  void companyLocations(){
    AppRoutes.pushScreen(context, AppRoutes.companyLocations,arguments: profiledata);
  }

  void digitalInformation(){
    AppRoutes.pushScreen(context, AppRoutes.digitalInformation,arguments: profiledata);
  }

  void legalDocuments(){
    AppRoutes.pushScreen(context, AppRoutes.legalDocuments,arguments: profiledata);
  }

  void subscriptionPlan(){
    AppRoutes.pushScreen(context, AppRoutes.subscriptionPlan);
  }

  void inAppCredit(){
    AppRoutes.pushScreen(context, AppRoutes.inAppCredit);
  }

  @override
  Widget build(BuildContext context) {
     return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.profile),
        elevation:0,
        leading: BackButton(color: AppColors.blackTextTheme),
      ),
      body: Column(
        children: [
           Padding(
            padding: const EdgeInsets.all(16.0),
            child: BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
              builder: (context, state) {
                if (state is ProfileMarchantLoaded) {
                  profiledata = state.profile;
                  final progress = ProfileProgress.calculateProfileProgress(profiledata!) / 100.0;
                  // Call profile-progress API
                  context.read<ProfileMarchantBloc>().add(
                    UpdateProfileProgressEvent(
                      profileProgress: progress * 100,
                    ),
                  );
                  return ProfileCompletenessCard(progress: progress);
                } else if (state is ProfileMarchantLoading) {
                  return loadingWidget();
                } else if (state is ProfileMarchantError) {
                  return const ProfileCompletenessCard(progress: 0.0);
                } else {
                  // Handle case where profiledata might be null
                  if (profiledata != null) {
                    return ProfileCompletenessCard(progress: ProfileProgress.calculateProfileProgress(profiledata!) / 100.0);
                  } else {
                    return const ProfileCompletenessCard(progress: 0.0);
                  }
                }
              },
            ),
          ),
          Expanded(
            child: ListView.builder(
            padding: const EdgeInsets.symmetric( horizontal: 20),
            itemCount: menuItems.length,
            itemBuilder: (context, index) {
              final item = menuItems[index];
              return ListTile(
                title: Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.blackTextTheme,
                  ),
                ),
                trailing: const Icon(Icons.chevron_right, color: Colors.black54),
                onTap: item.onTap,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
              );
            },
          ),
          ),
        ],
      ),
    );
  }

  Widget loadingWidget(){
    return SizedBox(
      height: 120,
      child: Center(
        child: const CircularProgressIndicator(
          strokeWidth: 3,color: AppColors.primaryTheme,),
      ));
  }
}


class ProfileCompletenessCard extends StatelessWidget {
  final double progress; // Value between 0.0 and 1.0

  const ProfileCompletenessCard({super.key, required this.progress});

  @override
  Widget build(BuildContext context) {
    final percentage = (progress * 100).toStringAsFixed(0);

    return SizedBox(
      height: 120,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Text(
            AppStrings.profileCompletenessTitle,
            style: TextStyle(
              color: AppColors.blackTextTheme,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 6),
          const Text(
            AppStrings.profileCompletenessSubtitle,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.primaryTheme,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                "$percentage%",
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: LinearProgressIndicator(
                    value: progress,
                    minHeight: 10,
                    backgroundColor: AppColors.progressBarBgTheme,
                    valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primaryTheme),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Divider(color:AppColors.borderTheme,thickness: 1,)
        ],
      ),
    );
  }
}



