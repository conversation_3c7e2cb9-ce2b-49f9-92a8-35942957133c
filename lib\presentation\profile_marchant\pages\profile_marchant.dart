import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/presentation/settings/pages/settings_page.dart';

class ProfileMarchant extends StatefulWidget {
  const ProfileMarchant({super.key});

  @override
  State<ProfileMarchant> createState() => _ProfileMarchantState();
}

class _ProfileMarchantState extends State<ProfileMarchant> {
   late final List<MenuItem> menuItems;

   @override
  void initState() {
    super.initState();
    menuItems = [
      MenuItem(title: AppStrings.companyDetails, onTap: () {}),
      MenuItem(title: AppStrings.industryDetails, onTap: () {}),
      MenuItem(title: AppStrings.companyLocation, onTap: () {}),
      MenuItem(title: AppStrings.digitalInformation, onTap: () {}),
      MenuItem(title: AppStrings.legalDocuments, onTap: () {}),
      MenuItem(title: AppStrings.payment, onTap: () {}),
      MenuItem(title: AppStrings.subscriptionPlan, onTap: () {}),
    ];
  }

  @override
  Widget build(BuildContext context) {
     return Scaffold(
      backgroundColor: AppColors.backgroundTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.settings),
        elevation: 0,
        leading: BackButton(color: AppColors.blackTextTheme),
      ),
      body: Column(
        children: [
           Padding(
            padding: const EdgeInsets.all(16.0),
            child: ProfileCompletenessCard(progress: 0.5), // 5% progress
          ),
          Expanded(
            child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            itemCount: menuItems.length,
            itemBuilder: (context, index) {
              final item = menuItems[index];
              return ListTile(
                title: Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.blackTextTheme,
                  ),
                ),
                trailing: const Icon(Icons.chevron_right, color: Colors.black54),
                onTap: item.onTap,
                contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
              );
            },
          ),
          ),
        ],
      ),
    );
  }
}


class ProfileCompletenessCard extends StatelessWidget {
  final double progress; // Value between 0.0 and 1.0

  const ProfileCompletenessCard({super.key, required this.progress});

  @override
  Widget build(BuildContext context) {
    final percentage = (progress * 100).toStringAsFixed(0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Text(
          AppStrings.profileCompletenessTitle,
          style: TextStyle(
            color: AppColors.blackTextTheme,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 6),
        const Text(
          AppStrings.profileCompletenessSubtitle,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.primaryTheme,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Text(
              "$percentage%",
              style: const TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: LinearProgressIndicator(
                  value: progress,
                  minHeight: 10,
                  backgroundColor: AppColors.progressBarBgTheme,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primaryTheme),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Divider(color:AppColors.borderTheme,thickness: 1,)
      ],
    );
  }
}
