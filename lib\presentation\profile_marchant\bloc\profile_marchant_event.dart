import 'dart:io';

abstract class ProfileMarchantEvent {}

class GetProfileEvent extends ProfileMarchantEvent {}

class UploadImageEvent extends ProfileMarchantEvent {
  final File file;
  final String fileName;

  UploadImageEvent({
    required this.file,
    required this.fileName,
  });
}

class UpdateCompanyDetailsEvent extends ProfileMarchantEvent {
  final String companyName;
  final String logo;
  final String description;
  final String detailedDescription;

  UpdateCompanyDetailsEvent({
    required this.companyName,
    required this.logo,
    required this.description,
    required this.detailedDescription,
  });
}

class UpdateIndustryDetailsEvent extends ProfileMarchantEvent {
  final List<String> industries;

  UpdateIndustryDetailsEvent({
    required this.industries,
  });
}

class UpdateCompanyLocationsEvent extends ProfileMarchantEvent {
  final String country;
  final String city;
  final String address;
  final String poBox;

  UpdateCompanyLocationsEvent({
    required this.country,
    required this.city,
    required this.address,
    required this.poBox,
  });
}

class UpdateDigitalInformationEvent extends ProfileMarchantEvent {
  final String website;
  final String facebook;
  final String twitter;
  final String linkedin;
  final String youtube;
  final String tiktok;
  final String snapchat;

  UpdateDigitalInformationEvent({
    required this.website,
    required this.facebook,
    required this.twitter,
    required this.linkedin,
    required this.youtube,
    required this.tiktok,
    required this.snapchat,
  });
}

class UpdateLegalDocumentsEvent extends ProfileMarchantEvent {
  final String companyName;
  final String companyLegalName;
  final String companySize;
  final String crNumber;
  final int investedCapital;
  final String investedCapitalUnit;
  final String crDocument;
  final String licenseDocument;

  UpdateLegalDocumentsEvent({
    required this.companyName,
    required this.companyLegalName,
    required this.companySize,
    required this.crNumber,
    required this.investedCapital,
    required this.investedCapitalUnit,
    required this.crDocument,
    required this.licenseDocument,
  });
}

class GetCountriesEvent extends ProfileMarchantEvent {}

class GetCitiesEvent extends ProfileMarchantEvent {
  final String countryId;

  GetCitiesEvent({required this.countryId});
}

class GetIndustriesEvent extends ProfileMarchantEvent {}
