import 'dart:io';

abstract class ProfileMarchantEvent {}

class GetProfileEvent extends ProfileMarchantEvent {}

class UploadImageEvent extends ProfileMarchantEvent {
  final File file;
  final String fileName;

  UploadImageEvent({
    required this.file,
    required this.fileName,
  });
}

class UpdateCompanyDetailsEvent extends ProfileMarchantEvent {
  final String companyName;
  final String logo;
  final String description;
  final String detailedDescription;

  UpdateCompanyDetailsEvent({
    required this.companyName,
    required this.logo,
    required this.description,
    required this.detailedDescription,
  });
}

class UpdateIndustryDetailsEvent extends ProfileMarchantEvent {
  final List<String> industries;

  UpdateIndustryDetailsEvent({
    required this.industries,
  });
}

class UpdateCompanyLocationsEvent extends ProfileMarchantEvent {
  final String country;
  final String city;
  final String address;
  final String poBox;

  UpdateCompanyLocationsEvent({
    required this.country,
    required this.city,
    required this.address,
    required this.poBox,
  });
}

class GetCountriesEvent extends ProfileMarchantEvent {}

class GetCitiesEvent extends ProfileMarchantEvent {
  final String countryId;

  GetCitiesEvent({required this.countryId});
}

class GetIndustriesEvent extends ProfileMarchantEvent {}
