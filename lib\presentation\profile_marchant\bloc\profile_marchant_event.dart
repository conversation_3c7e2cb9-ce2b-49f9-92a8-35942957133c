import 'dart:io';

abstract class ProfileMarchantEvent {}

class GetProfileEvent extends ProfileMarchantEvent {}

class UploadImageEvent extends ProfileMarchantEvent {
  final File file;
  final String fileName;

  UploadImageEvent({
    required this.file,
    required this.fileName,
  });
}

class UpdateCompanyDetailsEvent extends ProfileMarchantEvent {
  final String companyName;
  final String logo;
  final String description;
  final String detailedDescription;

  UpdateCompanyDetailsEvent({
    required this.companyName,
    required this.logo,
    required this.description,
    required this.detailedDescription,
  });
}

class UpdateIndustryDetailsEvent extends ProfileMarchantEvent {
  final List<String> industries;

  UpdateIndustryDetailsEvent({
    required this.industries,
  });
}
