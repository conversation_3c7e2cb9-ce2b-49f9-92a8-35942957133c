import 'dart:io';

import 'package:equatable/equatable.dart';

abstract class ProfileMarchantEvent extends Equatable{}

class GetProfileEvent extends ProfileMarchantEvent {
  @override
  List<Object?> get props => [];
  
}

class UploadImageEvent extends ProfileMarchantEvent {
  final File file;
  final String fileName;
  final String uploadId;

  UploadImageEvent({
    required this.file,
    required this.fileName,
    required this.uploadId
  });
  
  @override
  List<Object?> get props =>  [file, fileName, uploadId];
}

class UpdateCompanyDetailsEvent extends ProfileMarchantEvent {
  final String companyName;
  final String logo;
  final String description;
  final String detailedDescription;

  UpdateCompanyDetailsEvent({
    required this.companyName,
    required this.logo,
    required this.description,
    required this.detailedDescription,
  });
  
  @override
  List<Object?> get props =>  [companyName, logo, description, detailedDescription];
}

class UpdateIndustryDetailsEvent extends ProfileMarchantEvent {
  final List<String> industries;

  UpdateIndustryDetailsEvent({
    required this.industries,
  });
  @override
  List<Object?> get props =>  [industries];
}

class UpdateCompanyLocationsEvent extends ProfileMarchantEvent {
  final String country;
  final String city;
  final String address;
  final String poBox;

  UpdateCompanyLocationsEvent({
    required this.country,
    required this.city,
    required this.address,
    required this.poBox,
  });
  @override
  List<Object?> get props =>  [country, city, address, poBox];
}

class UpdateDigitalInformationEvent extends ProfileMarchantEvent {
  final String website;
  final String facebook;
  final String twitter;
  final String linkedin;
  final String youtube;
  final String tiktok;
  final String snapchat;

  UpdateDigitalInformationEvent({
    required this.website,
    required this.facebook,
    required this.twitter,
    required this.linkedin,
    required this.youtube,
    required this.tiktok,
    required this.snapchat,
  });
  @override
  List<Object?> get props =>  [website, facebook, twitter, linkedin, youtube, tiktok, snapchat];
}

class UpdateLegalDocumentsEvent extends ProfileMarchantEvent {
  final String companyLegalName;
  final String companySize;
  final String crNumber;
  final int investedCapital;
  final String investedCapitalUnit;
  final String? crDocument;
  final String? licenseDocument;

  UpdateLegalDocumentsEvent({
    required this.companyLegalName,
    required this.companySize,
    required this.crNumber,
    required this.investedCapital,
    required this.investedCapitalUnit,
     this.crDocument,
     this.licenseDocument,
  });
  @override
  List<Object?> get props =>  [companyLegalName, companySize, crNumber, investedCapital, investedCapitalUnit, crDocument, licenseDocument];
}

class GetCountriesEvent extends ProfileMarchantEvent {  
  @override
  List<Object?> get props => [];
}

class GetCitiesEvent extends ProfileMarchantEvent {
  final String countryId;

  GetCitiesEvent({required this.countryId});
  @override
  List<Object?> get props =>  [countryId];
}

class GetIndustriesEvent extends ProfileMarchantEvent {
  @override
  List<Object?> get props => [];
}

class GetFileMetadataEvent extends ProfileMarchantEvent {
  final String fileId;

  GetFileMetadataEvent({required this.fileId});

  @override
  List<Object?> get props => [fileId];
}

class UpdateProfileProgressEvent extends ProfileMarchantEvent {
  final double profileProgress;

  UpdateProfileProgressEvent({required this.profileProgress});
  
  @override
  List<Object?> get props => [profileProgress];
}
