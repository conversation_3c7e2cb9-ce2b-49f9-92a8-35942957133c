import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/utils/preferences.dart';
import '../../../../domain/usecases/login_usecase.dart';
import '../../../../domain/usecases/logout_usecase.dart';
import '../../../../domain/usecases/forgot_password_usecase.dart';
import 'login_event.dart';
import 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final ForgotPasswordUseCase forgotPasswordUseCase;

  LoginBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.forgotPasswordUseCase,
  }) : super(const LoginInitial()) {
    on<LoginSubmitted>(_onLoginSubmitted);
    on<LogoutRequested>(_onLogoutRequested);
    on<CheckLoginStatus>(_onCheckLoginStatus);
    on<ForgotPasswordSubmitted>(_onForgotPasswordSubmitted);
  }

  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<LoginState> emit,
  ) async {
    emit(const LoginLoading());

    final result = await loginUseCase(
      LoginParams(
        email: event.email,
        password: event.password,
      ),
    );

    await result.fold(
      (failure) async => emit(LoginFailure(message: failure.message)),
      (response) async {
        // Save tokens
        await Preferences.saveTokens(
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
        );
        // Decode user from access token and save
        await Preferences.saveUserFromToken(response.accessToken);
        final user = await Preferences.getUser();

        if (!emit.isDone && user != null) {
          emit(LoginSuccess(user: user));
        }
      },
    );
  }

  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<LoginState> emit,
  ) async {
    final result = await logoutUseCase();

    await result.fold(
      (failure) async => emit(LogoutFailure(message: failure.message)),
      (_) async {
        if (!emit.isDone) {
          emit(const LogoutSuccess());
        }
      },
    );
  }

  Future<void> _onCheckLoginStatus(
    CheckLoginStatus event,
    Emitter<LoginState> emit,
  ) async {
    // This would typically check if user is already logged in
    // For now, we'll emit initial state
    emit(const LoginStatusChecked(isLoggedIn: false));
  }

  Future<void> _onForgotPasswordSubmitted(
    ForgotPasswordSubmitted event,
    Emitter<LoginState> emit,
  ) async {
    emit(const ForgotPasswordLoading());

    final result = await forgotPasswordUseCase(
      ForgotPasswordParams(email: event.email),
    );

    result.fold(
      (failure) => emit(ForgotPasswordFailure(message: failure.message)),
      (message) => emit(ForgotPasswordSuccess(message: message)),
    );
  }
}
