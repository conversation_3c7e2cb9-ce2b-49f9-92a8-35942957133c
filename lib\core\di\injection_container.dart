import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../network/dio_client.dart';
import '../services/token_manager.dart';
import '../services/auto_refresh_service.dart';
import '../services/auto_logout_service.dart';
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/forgot_password_usecase.dart';
import '../../domain/usecases/upload_file_usecase.dart';
import '../../domain/usecases/update_company_details_usecase.dart';
import '../../domain/usecases/update_industry_details_usecase.dart';
import '../../domain/usecases/update_company_locations_usecase.dart';
import '../../domain/repositories/media_repository.dart';
import '../../data/repositories/media_repository_impl.dart';
import '../../data/datasources/media_remote_data_source.dart';
import '../../data/datasources/profile_marchant_remote_data_source.dart';
import '../../data/datasources/territory_remote_data_source.dart';
import '../../data/datasources/industry_remote_data_source.dart';
import '../../data/repositories/profile_marchant_repository_impl.dart';
import '../../data/repositories/territory_repository_impl.dart';
import '../../data/repositories/industry_repository_impl.dart';
import '../../domain/repositories/profile_marchant_repository.dart';
import '../../domain/repositories/territory_repository.dart';
import '../../domain/repositories/industry_repository.dart';
import '../../domain/usecases/get_profile_usecase.dart';
import '../../domain/usecases/get_countries_usecase.dart';
import '../../domain/usecases/get_cities_usecase.dart';
import '../../domain/usecases/get_industries_usecase.dart';
import '../../presentation/auth/bloc/login/login_bloc.dart';
import '../../presentation/auth/bloc/signup/signup_bloc.dart';
import '../../presentation/profile_marchant/bloc/profile_marchant_bloc.dart';

final serviceLocator = GetIt.instance;

Future<void> init() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  serviceLocator.registerLazySingleton(() => sharedPreferences);
  
  // Core
  serviceLocator.registerLazySingleton(() {
    final dioClient = DioClient();
    // Set token manager after both are registered
    return dioClient;
  });
  
  // Data sources
  serviceLocator.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<MediaRemoteDataSource>(
    () => MediaRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<ProfileMarchantRemoteDataSource>(
    () => ProfileMarchantRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<TerritoryRemoteDataSource>(
    () => TerritoryRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<IndustryRemoteDataSource>(
    () => IndustryRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  
  // Repositories
  serviceLocator.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: serviceLocator(),
      sharedPreferences: serviceLocator(),
    ),
  );
  serviceLocator.registerLazySingleton<MediaRepository>(
    () => MediaRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<ProfileMarchantRepository>(
    () => ProfileMarchantRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<TerritoryRepository>(
    () => TerritoryRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<IndustryRepository>(
    () => IndustryRepositoryImpl(remoteDataSource: serviceLocator()),
  );

  // Use cases
  serviceLocator.registerLazySingleton(() => LoginUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => LogoutUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => RegisterUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => ForgotPasswordUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UploadFileUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetProfileUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateCompanyDetailsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateIndustryDetailsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateCompanyLocationsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetCountriesUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetCitiesUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetIndustriesUseCase(serviceLocator()));

  // Services
  serviceLocator.registerLazySingleton(() => TokenManager(
    authDataSource: serviceLocator(),
  ));

  serviceLocator.registerLazySingleton(() => AutoRefreshService(
    tokenManager: serviceLocator(),
  ));

  serviceLocator.registerFactory(() => AutoLogoutService(
    onAutoLogout: () {
      // This will be handled by the LoginBloc
      // The callback will be set up in the LoginBloc constructor
    },
  ));
  
  // Blocs
  serviceLocator.registerFactory(
    () => LoginBloc(
      loginUseCase: serviceLocator(),
      logoutUseCase: serviceLocator(),
      forgotPasswordUseCase: serviceLocator(),
      tokenManager: serviceLocator(),
      autoRefreshService: serviceLocator(),
      autoLogoutService: serviceLocator(),
    ),
  );
  serviceLocator.registerFactory(
    () => SignupBloc(
      registerUseCase: serviceLocator(),
      uploadFileUseCase: serviceLocator(),
    ),
  );
  serviceLocator.registerLazySingleton(
    () => ProfileMarchantBloc(
      getProfileUseCase: serviceLocator(),
      uploadFileUseCase: serviceLocator(),
      updateCompanyDetailsUseCase: serviceLocator(),
      updateIndustryDetailsUseCase: serviceLocator(),
      updateCompanyLocationsUseCase: serviceLocator(),
      getCountriesUseCase: serviceLocator(),
      getCitiesUseCase: serviceLocator(),
      getIndustriesUseCase: serviceLocator(),
    ),
  );
}
