import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../models/industry_model.dart';

abstract class IndustryRemoteDataSource {
  Future<IndustryResponse> getIndustries();
}

class IndustryRemoteDataSourceImpl implements IndustryRemoteDataSource {
  final DioClient dioClient;

  IndustryRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<IndustryResponse> getIndustries() async {
    final response = await dioClient.get(
      AppConstants.territoryIndustryEndpoint,
    );
    return IndustryResponse.fromJson(response.data as Map<String, dynamic>);
  }
}
