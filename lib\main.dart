import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_bloc.dart';
import 'core/di/injection_container.dart' as di;
import 'core/localization/app_localizations.dart';
import 'core/routes/app_routes.dart';
import 'presentation/auth/bloc/language/language_bloc.dart';
import 'presentation/auth/bloc/login/login_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await di.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<LoginBloc>(
          create: (context) => di.serviceLocator<LoginBloc>(),
        ),
        BlocProvider<LanguageBloc>(
          create: (context) => LanguageBloc(),
        ),
        BlocProvider<TabsBloc>(
          create: (context) => TabsBloc(),
        ),
      ],
      child: BlocBuilder<LanguageBloc, LanguageState>(
        builder: (context, state) {
          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            locale: state.locale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale(AppConstants.langEn, ''), // English
              Locale(AppConstants.langAr, ''), // Arabic
            ],
            onGenerateRoute: AppRoutes.generateRoute,
            initialRoute: AppRoutes.root,
          );
        },
      ),
    );
  }
}


