class ProfileMarchantModel {
  final String id;
  final String companyName;
  final String firstName;
  final String lastName;
  final String email;
  final String type;
  final String userId;
  final String createdAt;
  final String updatedAt;
  final int version;
  final ProfileAttributes attributes;
  final CompanyModel company;

  ProfileMarchantModel({
    required this.id,
    required this.companyName,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.type,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
    required this.attributes,
    required this.company,
  });

  factory ProfileMarchantModel.fromJson(Map<String, dynamic> json) {
    return ProfileMarchantModel(
      id: json['_id'] as String,
      companyName: json['companyName'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      type: json['type'] as String,
      userId: json['userId'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      version: json['__v'] as int,
      attributes: ProfileAttributes.fromJson(json['attributes'] as Map<String, dynamic>),
      company: CompanyModel.fromJson(json['company'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'companyName': companyName,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'type': type,
      'userId': userId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      '__v': version,
      'attributes': attributes.toJson(),
      'company': company.toJson(),
    };
  }
}

class ProfileAttributes {
  final int profileProgress;
  final CompanyDetails companyDetails;
  final List<String> savedSellers;
  final CompanyLocations companyLocations;
  final List<String> companyIndustries;
  final CompanyDigitalInformations companyDigitalInformations;
  final bool hasInvited;
  final BankDetails bankDetails;

  ProfileAttributes({
    required this.profileProgress,
    required this.companyDetails,
    required this.savedSellers,
    required this.companyLocations,
    required this.companyIndustries,
    required this.companyDigitalInformations,
    required this.hasInvited,
    required this.bankDetails,
  });

  factory ProfileAttributes.fromJson(Map<String, dynamic> json) {
    return ProfileAttributes(
      profileProgress: json['profileProgress'] as int,
      companyDetails: CompanyDetails.fromJson(json['companyDetails'] as Map<String, dynamic>),
      savedSellers: List<String>.from(json['savedSellers'] as List),
      companyLocations: CompanyLocations.fromJson(json['companyLocations'] as Map<String, dynamic>),
      companyIndustries: List<String>.from(json['companyIndustries'] as List),
      companyDigitalInformations: CompanyDigitalInformations.fromJson(json['companyDigitalInformations'] as Map<String, dynamic>),
      hasInvited: json['hasInvited'] as bool,
      bankDetails: BankDetails.fromJson(json['bankDetails'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'profileProgress': profileProgress,
      'companyDetails': companyDetails.toJson(),
      'savedSellers': savedSellers,
      'companyLocations': companyLocations.toJson(),
      'companyIndustries': companyIndustries,
      'companyDigitalInformations': companyDigitalInformations.toJson(),
      'hasInvited': hasInvited,
      'bankDetails': bankDetails.toJson(),
    };
  }
}

class CompanyDetails {
  final String logo;
  final String description;
  final String detailedDescription;
  final String? companyName;

  CompanyDetails({
    required this.logo,
    required this.description,
    required this.detailedDescription,
    this.companyName,
  });

  factory CompanyDetails.fromJson(Map<String, dynamic> json) {
    return CompanyDetails(
      logo: json['logo'] as String,
      description: json['description'] as String,
      detailedDescription: json['detailedDescription'] as String,
      companyName: json['companyName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'logo': logo,
      'description': description,
      'detailedDescription': detailedDescription,
      'companyName': companyName,
    };
  }
}

class CompanyLocations {
  final String country;
  final String city;
  final String address;
  final String poBox;

  CompanyLocations({
    required this.country,
    required this.city,
    required this.address,
    required this.poBox,
  });

  factory CompanyLocations.fromJson(Map<String, dynamic> json) {
    return CompanyLocations(
      country: json['country'] as String,
      city: json['city'] as String,
      address: json['address'] as String,
      poBox: json['poBox'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'city': city,
      'address': address,
      'poBox': poBox,
    };
  }
}

class CompanyDigitalInformations {
  final String website;
  final String facebook;
  final String twitter;
  final String linkedin;
  final String youtube;
  final String tiktok;
  final String snapchat;

  CompanyDigitalInformations({
    required this.website,
    required this.facebook,
    required this.twitter,
    required this.linkedin,
    required this.youtube,
    required this.tiktok,
    required this.snapchat,
  });

  factory CompanyDigitalInformations.fromJson(Map<String, dynamic> json) {
    return CompanyDigitalInformations(
      website: json['website'] as String,
      facebook: json['facebook'] as String,
      twitter: json['twitter'] as String,
      linkedin: json['linkedin'] as String,
      youtube: json['youtube'] as String,
      tiktok: json['tiktok'] as String,
      snapchat: json['snapchat'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'website': website,
      'facebook': facebook,
      'twitter': twitter,
      'linkedin': linkedin,
      'youtube': youtube,
      'tiktok': tiktok,
      'snapchat': snapchat,
    };
  }
}

class BankDetails {
  final String iban;
  final String accountName;
  final String bankName;
  final String ibanCertificate;

  BankDetails({
    required this.iban,
    required this.accountName,
    required this.bankName,
    required this.ibanCertificate,
  });

  factory BankDetails.fromJson(Map<String, dynamic> json) {
    return BankDetails(
      iban: json['iban'] as String,
      accountName: json['accountName'] as String,
      bankName: json['bankName'] as String,
      ibanCertificate: json['iban Certificate'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iban': iban,
      'accountName': accountName,
      'bankName': bankName,
      'iban Certificate': ibanCertificate,
    };
  }
}

class CompanyModel {
  final String id;
  final String crNumber;
  final String userId;
  final String companySize;
  final String companyLegalName;
  final int investedCapital;
  final String investedCapitalUnit;
  final String crDocument;
  final String licenseDocument;
  final String createdAt;
  final String updatedAt;
  final int version;

  CompanyModel({
    required this.id,
    required this.crNumber,
    required this.userId,
    required this.companySize,
    required this.companyLegalName,
    required this.investedCapital,
    required this.investedCapitalUnit,
    required this.crDocument,
    required this.licenseDocument,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
  });

  factory CompanyModel.fromJson(Map<String, dynamic> json) {
    return CompanyModel(
      id: json['_id'] as String,
      crNumber: json['crNumber'] as String,
      userId: json['userId'] as String,
      companySize: json['companySize'] as String,
      companyLegalName: json['companyLegalName'] as String,
      investedCapital: json['investedCapital'] as int,
      investedCapitalUnit: json['investedCapitalUnit'] as String,
      crDocument: json['crDocument'] as String,
      licenseDocument: json['licenseDocument'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      version: json['__v'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'crNumber': crNumber,
      'userId': userId,
      'companySize': companySize,
      'companyLegalName': companyLegalName,
      'investedCapital': investedCapital,
      'investedCapitalUnit': investedCapitalUnit,
      'crDocument': crDocument,
      'licenseDocument': licenseDocument,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      '__v': version,
    };
  }
}
