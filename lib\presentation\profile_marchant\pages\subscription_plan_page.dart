import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';

class SubscriptionPlanPage extends StatelessWidget {
  const SubscriptionPlanPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        title: const Text(
          AppStrings.subscriptionPlan,
          style: TextStyle(
            color: AppColors.blackTextTheme,
          ),
        ),
        backgroundColor: AppColors.whiteTheme,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColors.blackTextTheme,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Coming Soon Icon
              Icon(
                Icons.rocket_launch_outlined,
                size: 80,
                color: AppColors.primaryTheme,
              ),
              SizedBox(height: 32),
              
              // Coming Soon Text
              Text(
                'Coming Soon',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryTheme,
                  letterSpacing: 1.2,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              
              // Description Text
              Text(
                'We\'re working hard to bring you something amazing. Stay tuned!',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.blackTextTheme,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 48),
              
              // Decorative Elements
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  BuildDot(),
                  SizedBox(width: 8),
                  BuildDot(),
                  SizedBox(width: 8),
                  BuildDot(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BuildDot extends StatelessWidget {
  const BuildDot({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 8,
      height: 8,
      decoration: const BoxDecoration(
        color: AppColors.primaryTheme,
        shape: BoxShape.circle,
      ),
    );
  }
}
