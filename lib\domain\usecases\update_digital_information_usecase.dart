import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateDigitalInformationUseCase {
  final ProfileMarchantRepository repository;

  UpdateDigitalInformationUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(UpdateDigitalInformationParams params) async {
    return await repository.updateDigitalInformation(
      website: params.website,
      facebook: params.facebook,
      twitter: params.twitter,
      linkedin: params.linkedin,
      youtube: params.youtube,
      tiktok: params.tiktok,
      snapchat: params.snapchat,
    );
  }
}

class UpdateDigitalInformationParams {
  final String website;
  final String facebook;
  final String twitter;
  final String linkedin;
  final String youtube;
  final String tiktok;
  final String snapchat;

  UpdateDigitalInformationParams({
    required this.website,
    required this.facebook,
    required this.twitter,
    required this.linkedin,
    required this.youtube,
    required this.tiktok,
    required this.snapchat,
  });
}
