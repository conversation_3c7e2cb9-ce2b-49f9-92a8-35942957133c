import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/error/exceptions.dart';
import '../../domain/repositories/territory_repository.dart';
import '../datasources/territory_remote_data_source.dart';
import '../models/territory_model.dart';

class TerritoryRepositoryImpl implements TerritoryRepository {
  final TerritoryRemoteDataSource remoteDataSource;

  TerritoryRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, TerritoryResponse>> getCountries() async {
    try {
      final result = await remoteDataSource.getCountries();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CityResponse>> getCitiesByCountryId(String countryId) async {
    try {
      final result = await remoteDataSource.getCitiesByCountryId(countryId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
