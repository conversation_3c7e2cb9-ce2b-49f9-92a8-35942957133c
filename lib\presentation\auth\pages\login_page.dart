import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_bloc.dart';
import 'package:wesell/presentation/tabs/bloc/tabs_bloc/tabs_event.dart';
import 'package:wesell/presentation/widgets/password_visibility_icon.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/utils/validators.dart';
import '../../../core/constants/app_helper.dart';
import '../bloc/login/login_bloc.dart';
import '../bloc/login/login_event.dart';
import '../bloc/login/login_state.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController(text: "<EMAIL>");
  final _passwordController = TextEditingController(text: "Admin123@");
  bool _obscurePassword = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: AppColors.whiteTheme,
        body: SingleChildScrollView(
          child: BlocListener<LoginBloc, LoginState>(
            listener: (context, state) {
              if (state is LoginSuccess) {
                AppHelper.user = state.user;
                context.read<TabsBloc>().add(TabChanged(0));
                AppRoutes.pushRoot(context, AppRoutes.tabs);
              } else if (state is LoginFailure) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 80),
                      Text(
                        AppStrings.logIn,
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryTheme,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 80),
                      // Email Field
                      CustomTextField(
                        controller: _emailController,
                        hintText: localizations.emailHint,
                        prefixIcon: Icons.email_outlined,
                        validator: Validators.validateEmail,
                        textInputAction: TextInputAction.next,
                      ),
                      const SizedBox(height: 16),
                      
                      // Password Field
                      CustomTextField(
                        controller: _passwordController,
                        hintText: localizations.passwordHint,
                        prefixIcon: Icons.lock_outline,
                        obscureText: !_obscurePassword,
                        suffixIcon: PasswordVisibilityIcon(
                          isVisible: _obscurePassword,
                          onTap: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                      ),
                      validator: Validators.validatePassword,
                      textInputAction: TextInputAction.done,
                      onFieldSubmitted: (_) => _handleLogin(),
                    ),
                      const SizedBox(height: 15),
                      Align(
                        alignment: Alignment.centerRight,
                        child: InkWell(
                          onTap: () {
                            AppRoutes.pushScreen(context, AppRoutes.forgotPassword);
                          },
                          child: Text(AppStrings.forgotPassword,  style: TextStyle(color: AppColors.primaryTheme, fontSize: 16)),
                        ),
                      ),
                      const SizedBox(height: 30),
                      // Login Button
                      BlocBuilder<LoginBloc, LoginState>(
                        builder: (context, state) {
                          return CustomButton(
                            backgroundColor: AppColors.primaryTheme,
                            text: localizations.loginButton,
                            onPressed: state is LoginLoading ? null : _handleLogin,
                            isLoading: state is LoginLoading,
                          );
                        },
                      ),
                      const SizedBox(height: 20),
                      //Don't have an account yet? Sign Up
                      Center(
                        child: Text.rich(
                          TextSpan(
                            text: AppStrings.dontHaveAccount,
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.textgreyTheme,
                            ),
                            children: [
                              TextSpan(
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                   // Navigate to get user type screen
                                    AppRoutes.pushScreen(context, AppRoutes.userRole);
                                  },
                                text: AppStrings.signUp,
                                style: const TextStyle(color:  AppColors.primaryTheme,fontSize: 16,),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<LoginBloc>().add(
        LoginSubmitted(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );
    }
  }
}
