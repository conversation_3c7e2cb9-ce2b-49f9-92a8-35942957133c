import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class DigitalInformationPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const DigitalInformationPage({super.key, required this.profileData});

  @override
  _DigitalInformationPageState createState() => _DigitalInformationPageState();
}

class _DigitalInformationPageState extends State<DigitalInformationPage> {
  final _formKey = GlobalKey<FormState>();
  final _websiteController = TextEditingController();
  final _facebookController = TextEditingController();
  final _twitterController = TextEditingController();
  final _linkedinController = TextEditingController();
  final _youtubeController = TextEditingController();
  final _tiktokController = TextEditingController();
  final _snapchatController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final digitalInfo = widget.profileData.attributes?.companyDigitalInformations;
    if (digitalInfo != null) {
      _websiteController.text = digitalInfo.website ?? '';
      _facebookController.text = digitalInfo.facebook ?? '';
      _twitterController.text = digitalInfo.twitter ?? '';
      _linkedinController.text = digitalInfo.linkedin ?? '';
      _youtubeController.text = digitalInfo.youtube ?? '';
      _tiktokController.text = digitalInfo.tiktok ?? '';
      _snapchatController.text = digitalInfo.snapchat ?? '';
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateDigitalInformationEvent(
        website: _websiteController.text.trim(),
        facebook: _facebookController.text.trim(),
        twitter: _twitterController.text.trim(),
        linkedin: _linkedinController.text.trim(),
        youtube: _youtubeController.text.trim(),
        tiktok: _tiktokController.text.trim(),
        snapchat: _snapchatController.text.trim(),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateDigitalInformationSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateDigitalInformationError) {
            _showSnackBar(state.message);
          }
        },
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            backgroundColor: AppColors.whiteTheme,
            appBar: AppBar(
              backgroundColor: AppColors.whiteTheme,
              title: const Text(AppStrings.digitalInformation),
              leading: const BackButton(color: AppColors.blackTextTheme),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                autovalidateMode: AutovalidateMode.onUserInteraction,
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Website Field
                    CustomTextField(
                      controller: _websiteController,
                      hintText: AppStrings.enterWebsiteUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 16),

                    // Facebook Field
                    CustomTextField(
                      controller: _facebookController,
                      hintText: AppStrings.enterFacebookUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 16),

                    // Twitter Field
                    CustomTextField(
                      controller: _twitterController,
                      hintText: AppStrings.enterTwitterUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 16),

                    // LinkedIn Field
                    CustomTextField(
                      controller: _linkedinController,
                      hintText: AppStrings.enterLinkedinUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 16),

                    // YouTube Field
                    CustomTextField(
                      controller: _youtubeController,
                      hintText: AppStrings.enterYoutubeUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 16),

                    // TikTok Field
                    CustomTextField(
                      controller: _tiktokController,
                      hintText: AppStrings.enterTiktokUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 16),

                    // Snapchat Field
                    CustomTextField(
                      controller: _snapchatController,
                      hintText: AppStrings.enterSnapchatUrl,
                      validator: Validators.validateUrl,
                    ),
                    const SizedBox(height: 24),

                    // Save Button
                    BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                      builder: (context, state) {
                        return CustomButton(
                          width: double.infinity,
                          backgroundColor: AppColors.primaryTheme,
                          text: AppStrings.saveChanges,
                          onPressed: state is UpdateDigitalInformationLoading ? null : _handleFormSubmit,
                          isLoading: state is UpdateDigitalInformationLoading,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _websiteController.dispose();
    _facebookController.dispose();
    _twitterController.dispose();
    _linkedinController.dispose();
    _youtubeController.dispose();
    _tiktokController.dispose();
    _snapchatController.dispose();
    super.dispose();
  }
}
