import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../repositories/auth_repository.dart';
import '../../data/models/login_response.dart';

class RefreshTokenUseCase {
  final AuthRepository repository;

  RefreshTokenUseCase(this.repository);

  Future<Either<Failure, LoginResponse>> call(String refreshToken) async {
    return await repository.refreshToken(refreshToken: refreshToken);
  }
}
