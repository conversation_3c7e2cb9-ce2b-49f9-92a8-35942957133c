import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/profile_marchant_model.dart';
import '../../data/models/update_company_details_response.dart';

abstract class ProfileMarchantRepository {
  Future<Either<Failure, ProfileMarchantModel>> getProfile();
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  });
}
