import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/data/models/industry_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class IndustryDetailsPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const IndustryDetailsPage({super.key, required this.profileData});

  @override
  _IndustryDetailsPageState createState() => _IndustryDetailsPageState();
}

class _IndustryDetailsPageState extends State<IndustryDetailsPage> {
  final _formKey = GlobalKey<FormState>();
  IndustryModel? _selectedIndustry;
  List<String> _selectedIndustries = [];
  String? _dropdownError;

  List<IndustryModel> _availableIndustries = [];

  @override
  void initState() {
    super.initState();
    // Initialize with existing industries from profile
    _selectedIndustries = widget.profileData.attributes?.companyIndustries != null
        ? List<String>.from(widget.profileData.attributes!.companyIndustries!)
        : [];
    // Load industries when page initializes
    context.read<ProfileMarchantBloc>().add(GetIndustriesEvent());
  }

  void _addIndustry(IndustryModel industry) {
    if (!_selectedIndustries.contains(industry.industryName)) {
      setState(() {
        _selectedIndustries.add(industry.industryName);
        _selectedIndustry = null;
        _dropdownError = null;
      });
    }
  }

  void _removeIndustry(String industry) {
    setState(() {
      _selectedIndustries.remove(industry);
    });
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _handleFormSubmit() {
    if (_selectedIndustries.isEmpty) {
      setState(() {
        _dropdownError = AppStrings.pleaseSelectIndustries;
      });
      return;
    }

    context.read<ProfileMarchantBloc>().add(UpdateIndustryDetailsEvent(
      industries: _selectedIndustries,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateIndustryDetailsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateIndustryDetailsError) {
            _showSnackBar(state.message);
          } else if (state is IndustriesLoaded) {
            setState(() {
              _availableIndustries = state.industries;
            });
          } else if (state is IndustriesError) {
            _showSnackBar('Error loading industries: ${state.message}');
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
            backgroundColor: AppColors.whiteTheme,
            title: const Text(AppStrings.industryDetails),
            leading: const BackButton(color: AppColors.blackTextTheme),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      final isLoading = state is IndustriesLoading;
                      return CustomDropdownFormField<IndustryModel>(
                        value: _selectedIndustry,
                        items: _availableIndustries,
                        hintText: AppStrings.industriesSearch,
                        itemLabel: (item) => item.industryName,
                        onChanged: isLoading ? null : (value) {
                          if (value != null) {
                            _addIndustry(value);
                          }
                        },
                        validator: (_) => _dropdownError,
                        disabled: isLoading,
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  if (_selectedIndustries.isNotEmpty) ...[
                    Text(
                      AppStrings.selectedIndustries,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.blackTextTheme,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: _selectedIndustries.map((industry) {
                        return Chip(
                          label: Text(
                            industry,
                            style: const TextStyle(
                              color: AppColors.whiteTheme,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          backgroundColor: AppColors.primaryTheme,
                          deleteIcon: const Icon(
                            Icons.close,
                            color: AppColors.whiteTheme,
                            size: 18,
                          ),
                          onDeleted: () => _removeIndustry(industry),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 24),
                  ],
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      return CustomButton(
                        width: double.infinity,
                        backgroundColor: AppColors.primaryTheme,
                        text: AppStrings.saveChanges,
                        onPressed: state is UpdateIndustryDetailsLoading ? null : _handleFormSubmit,
                        isLoading: state is UpdateIndustryDetailsLoading,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
