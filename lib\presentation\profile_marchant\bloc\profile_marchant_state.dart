import 'package:equatable/equatable.dart';

import '../../../data/models/profile_marchant_model.dart';
import '../../../data/models/territory_model.dart';
import '../../../data/models/industry_model.dart';

abstract class ProfileMarchantState extends Equatable{}

class ProfileMarchantInitial extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ProfileMarchantLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ProfileMarchantLoaded extends ProfileMarchantState {
  final ProfileMarchantModel profile;

  ProfileMarchantLoaded({required this.profile});
  
  @override
  List<Object?> get props => [profile];
}

class ProfileMarchantError extends ProfileMarchantState {
  final String message;

  ProfileMarchantError({required this.message});
  @override
  List<Object?> get props => [message];
}

class ImageUploadLoading extends ProfileMarchantState {
  final String uploadId;

  ImageUploadLoading({required this.uploadId});
  @override
  List<Object?> get props => [];
}

class ImageUploadSuccess extends ProfileMarchantState {
  final String fileId;
  final String fileName;
  final String uploadId;

  ImageUploadSuccess({
    required this.fileId,
    required this.fileName,
    required this.uploadId,
  });
  @override
  List<Object?> get props => [fileId, fileName, uploadId];
}

class ImageUploadError extends ProfileMarchantState {
  final String message;
  final String uploadId;

  ImageUploadError({required this.message, required this.uploadId});
  @override
  List<Object?> get props => [message, uploadId];
}

class UpdateCompanyDetailsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateCompanyDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyDetailsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateIndustryDetailsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateIndustryDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateIndustryDetailsError extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyLocationsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateCompanyLocationsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsSuccess({required this.message});
    @override
  List<Object?> get props => [message];
}

class UpdateCompanyLocationsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateDigitalInformationLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateDigitalInformationSuccess extends ProfileMarchantState {
  final String message;

  UpdateDigitalInformationSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateDigitalInformationError extends ProfileMarchantState {
  final String message;

  UpdateDigitalInformationError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateLegalDocumentsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateLegalDocumentsSuccess extends ProfileMarchantState {
  final String message;

  UpdateLegalDocumentsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateLegalDocumentsError extends ProfileMarchantState {
  final String message;

  UpdateLegalDocumentsError({required this.message});
  @override
  List<Object?> get props => [message];
}

// Territory States
class CountriesLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class CountriesLoaded extends ProfileMarchantState {
  final List<CountryModel> countries;

  CountriesLoaded({required this.countries});
  @override
  List<Object?> get props => [countries];
}

class CountriesError extends ProfileMarchantState {
  final String message;

  CountriesError({required this.message});
  @override
  List<Object?> get props => [message];
}

class CitiesLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class CitiesLoaded extends ProfileMarchantState {
  final List<CityModel> cities;

  CitiesLoaded({required this.cities});
  @override
  List<Object?> get props => [cities];
}

class CitiesError extends ProfileMarchantState {
  final String message;

  CitiesError({required this.message});
  @override
  List<Object?> get props => [message];
}

// Industry States
class IndustriesLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class IndustriesLoaded extends ProfileMarchantState {
  final List<IndustryModel> industries;

  IndustriesLoaded({required this.industries});
  @override
  List<Object?> get props => [industries];
}

class IndustriesError extends ProfileMarchantState {
  final String message;

  IndustriesError({required this.message});
  @override
  List<Object?> get props => [message];
}

class FileMetadataLoading extends ProfileMarchantState {
  final String fileId;

  FileMetadataLoading({required this.fileId});

  @override
  List<Object?> get props => [fileId];
}

class FileMetadataLoaded extends ProfileMarchantState {
  final String fileId;
  final String name;
  final String ext;
  final int sizeInBytes;

  FileMetadataLoaded({
    required this.fileId,
    required this.name,
    required this.ext,
    required this.sizeInBytes,
  });

  @override
  List<Object?> get props => [fileId, name, ext, sizeInBytes];
}

class FileMetadataError extends ProfileMarchantState {
  final String fileId;
  final String message;

  FileMetadataError({required this.fileId, required this.message});

  @override
  List<Object?> get props => [fileId, message];
}

// Profile Update Progress States
class UpdateProfileProgressLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateProfileProgressSuccess extends ProfileMarchantState {
  final String message;

  UpdateProfileProgressSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateProfileProgressError extends ProfileMarchantState {
  final String message;

  UpdateProfileProgressError({required this.message});
  @override
  List<Object?> get props => [message];
}
