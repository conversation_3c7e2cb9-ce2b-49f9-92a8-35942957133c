import '../../../data/models/profile_marchant_model.dart';

abstract class ProfileMarchantState {}

class ProfileMarchantInitial extends ProfileMarchantState {}

class ProfileMarchantLoading extends ProfileMarchantState {}

class ProfileMarchantLoaded extends ProfileMarchantState {
  final ProfileMarchantModel profile;

  ProfileMarchantLoaded({required this.profile});
}

class ProfileMarchantError extends ProfileMarchantState {
  final String message;

  ProfileMarchantError({required this.message});
}

class ImageUploadLoading extends ProfileMarchantState {}

class ImageUploadSuccess extends ProfileMarchantState {
  final String fileId;
  final String fileName;

  ImageUploadSuccess({
    required this.fileId,
    required this.fileName,
  });
}

class ImageUploadError extends ProfileMarchantState {
  final String message;

  ImageUploadError({required this.message});
}

class UpdateCompanyDetailsLoading extends ProfileMarchantState {}

class UpdateCompanyDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsSuccess({required this.message});
}

class UpdateCompanyDetailsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsError({required this.message});
}
