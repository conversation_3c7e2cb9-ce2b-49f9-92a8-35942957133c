import 'package:equatable/equatable.dart';

import '../../../data/models/profile_marchant_model.dart';
import '../../../data/models/territory_model.dart';
import '../../../data/models/industry_model.dart';

abstract class ProfileMarchantState extends Equatable{}

class ProfileMarchantInitial extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ProfileMarchantLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ProfileMarchantLoaded extends ProfileMarchantState {
  final ProfileMarchantModel profile;

  ProfileMarchantLoaded({required this.profile});
  
  @override
  List<Object?> get props => [profile];
}

class ProfileMarchantError extends ProfileMarchantState {
  final String message;

  ProfileMarchantError({required this.message});
  @override
  List<Object?> get props => [message];
}

class ImageUploadLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class ImageUploadSuccess extends ProfileMarchantState {
  final String fileId;
  final String fileName;

  ImageUploadSuccess({
    required this.fileId,
    required this.fileName,
  });
  @override
  List<Object?> get props => [fileId, fileName];
}

class ImageUploadError extends ProfileMarchantState {
  final String message;

  ImageUploadError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyDetailsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateCompanyDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyDetailsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyDetailsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateIndustryDetailsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateIndustryDetailsSuccess extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsSuccess({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateIndustryDetailsError extends ProfileMarchantState {
  final String message;

  UpdateIndustryDetailsError({required this.message});
  @override
  List<Object?> get props => [message];
}

class UpdateCompanyLocationsLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class UpdateCompanyLocationsSuccess extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsSuccess({required this.message});
    @override
  List<Object?> get props => [message];
}

class UpdateCompanyLocationsError extends ProfileMarchantState {
  final String message;

  UpdateCompanyLocationsError({required this.message});
  @override
  List<Object?> get props => [message];
}

// Territory States
class CountriesLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class CountriesLoaded extends ProfileMarchantState {
  final List<CountryModel> countries;

  CountriesLoaded({required this.countries});
  @override
  List<Object?> get props => [countries];
}

class CountriesError extends ProfileMarchantState {
  final String message;

  CountriesError({required this.message});
  @override
  List<Object?> get props => [message];
}

class CitiesLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class CitiesLoaded extends ProfileMarchantState {
  final List<CityModel> cities;

  CitiesLoaded({required this.cities});
  @override
  List<Object?> get props => [cities];
}

class CitiesError extends ProfileMarchantState {
  final String message;

  CitiesError({required this.message});
  @override
  List<Object?> get props => [message];
}

// Industry States
class IndustriesLoading extends ProfileMarchantState {
  @override
  List<Object?> get props => [];
}

class IndustriesLoaded extends ProfileMarchantState {
  final List<IndustryModel> industries;

  IndustriesLoaded({required this.industries});
  @override
  List<Object?> get props => [industries];
}

class IndustriesError extends ProfileMarchantState {
  final String message;

  IndustriesError({required this.message});
  @override
  List<Object?> get props => [message];
}
