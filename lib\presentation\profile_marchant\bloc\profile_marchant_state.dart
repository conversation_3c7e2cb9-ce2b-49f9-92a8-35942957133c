import '../../../data/models/profile_marchant_model.dart';

abstract class ProfileMarchantState {}

class ProfileMarchantInitial extends ProfileMarchantState {}

class ProfileMarchantLoading extends ProfileMarchantState {}

class ProfileMarchantLoaded extends ProfileMarchantState {
  final ProfileMarchantModel profile;

  ProfileMarchantLoaded({required this.profile});
}

class ProfileMarchantError extends ProfileMarchantState {
  final String message;

  ProfileMarchantError({required this.message});
}
