import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../models/profile_marchant_model.dart';

abstract class ProfileMarchantRemoteDataSource {
  Future<ProfileMarchantModel> getProfile();
}

class ProfileMarchantRemoteDataSourceImpl implements ProfileMarchantRemoteDataSource {
  final DioClient dioClient;

  ProfileMarchantRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<ProfileMarchantModel> getProfile() async {
    final response = await dioClient.get(
      AppConstants.profileEndpoint,
    );
    return ProfileMarchantModel.fromJson(response.data as Map<String, dynamic>);
  }
}
