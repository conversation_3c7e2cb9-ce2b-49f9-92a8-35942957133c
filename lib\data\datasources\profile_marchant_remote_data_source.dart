import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../models/profile_marchant_model.dart';
import '../models/update_company_details_response.dart';

abstract class ProfileMarchantRemoteDataSource {
  Future<ProfileMarchantModel> getProfile();
  Future<UpdateCompanyDetailsResponse> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  });
}

class ProfileMarchantRemoteDataSourceImpl implements ProfileMarchantRemoteDataSource {
  final DioClient dioClient;

  ProfileMarchantRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<ProfileMarchantModel> getProfile() async {
    final response = await dioClient.get(
      AppConstants.profileEndpoint,
    );
    return ProfileMarchantModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<UpdateCompanyDetailsResponse> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  }) async {
    final response = await dioClient.put(
      AppConstants.profileEndpoint+AppConstants.companyDetailsEndpoint,
      data: {
        'companyName': companyName,
        'logo': logo,
        'description': description,
        'detailedDescription': detailedDescription,
      },
    );
    return UpdateCompanyDetailsResponse.fromJson(response.data as Map<String, dynamic>);
  }
}
