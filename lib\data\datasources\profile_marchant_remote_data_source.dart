import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../models/profile_marchant_model.dart';
import '../models/update_company_details_response.dart';

abstract class ProfileMarchantRemoteDataSource {
  Future<ProfileMarchantModel> getProfile();
  Future<UpdateCompanyDetailsResponse> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  });
  Future<UpdateCompanyDetailsResponse> updateIndustryDetails({
    required List<String> industries,
  });
  Future<UpdateCompanyDetailsResponse> updateCompanyLocations({
    required String country,
    required String city,
    required String address,
    required String poBox,
  });
  Future<UpdateCompanyDetailsResponse> updateDigitalInformation({
    required String website,
    required String facebook,
    required String twitter,
    required String linkedin,
    required String youtube,
    required String tiktok,
    required String snapchat,
  });
  Future<UpdateCompanyDetailsResponse> updateLegalDocuments({
    required String companyName,
    required String companyLegalName,
    required String companySize,
    required String crNumber,
    required int investedCapital,
    required String investedCapitalUnit,
    required String crDocument,
    required String licenseDocument,
  });
}

class ProfileMarchantRemoteDataSourceImpl implements ProfileMarchantRemoteDataSource {
  final DioClient dioClient;

  ProfileMarchantRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<ProfileMarchantModel> getProfile() async {
    final response = await dioClient.get(
      AppConstants.profileEndpoint,
    );
    return ProfileMarchantModel.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<UpdateCompanyDetailsResponse> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  }) async {
    final response = await dioClient.put(
      AppConstants.profileEndpoint+AppConstants.companyDetailsEndpoint,
      data: {
        'companyName': companyName,
        'logo': logo,
        'description': description,
        'detailedDescription': detailedDescription,
      },
    );
    return UpdateCompanyDetailsResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<UpdateCompanyDetailsResponse> updateIndustryDetails({
    required List<String> industries,
  }) async {
    final response = await dioClient.put(
      AppConstants.profileEndpoint+AppConstants.industryDetailsEndpoint,
      data: {
        'industries': industries,
      },
    );
    return UpdateCompanyDetailsResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<UpdateCompanyDetailsResponse> updateCompanyLocations({
    required String country,
    required String city,
    required String address,
    required String poBox,
  }) async {
    final response = await dioClient.put(
      AppConstants.profileEndpoint+AppConstants.companyLocationsEndpoint,
      data: {
        'country': country,
        'city': city,
        'address': address,
        'poBox': poBox,
      },
    );
    return UpdateCompanyDetailsResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<UpdateCompanyDetailsResponse> updateDigitalInformation({
    required String website,
    required String facebook,
    required String twitter,
    required String linkedin,
    required String youtube,
    required String tiktok,
    required String snapchat,
  }) async {
    final response = await dioClient.put(
      AppConstants.profileEndpoint+AppConstants.digitalInformationEndpoint,
      data: {
        'website': website,
        'facebook': facebook,
        'twitter': twitter,
        'linkedin': linkedin,
        'youtube': youtube,
        'tiktok': tiktok,
        'snapchat': snapchat,
      },
    );
    return UpdateCompanyDetailsResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<UpdateCompanyDetailsResponse> updateLegalDocuments({
    required String companyName,
    required String companyLegalName,
    required String companySize,
    required String crNumber,
    required int investedCapital,
    required String investedCapitalUnit,
    required String crDocument,
    required String licenseDocument,
  }) async {
    final response = await dioClient.put(
      AppConstants.profileEndpoint+AppConstants.legalDocumentsEndpoint,
      data: {
        'companyName': companyName,
        'companyLegalName': companyLegalName,
        'companySize': companySize,
        'crNumber': crNumber,
        'investedCapital': investedCapital,
        'investedCapitalUnit': investedCapitalUnit,
        'crDocument': crDocument,
        'licenseDocument': licenseDocument,
      },
    );
    return UpdateCompanyDetailsResponse.fromJson(response.data as Map<String, dynamic>);
  }
}
