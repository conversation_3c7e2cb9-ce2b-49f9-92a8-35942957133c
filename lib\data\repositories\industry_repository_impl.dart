import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/error/exceptions.dart';
import '../../domain/repositories/industry_repository.dart';
import '../datasources/industry_remote_data_source.dart';
import '../models/industry_model.dart';

class IndustryRepositoryImpl implements IndustryRepository {
  final IndustryRemoteDataSource remoteDataSource;

  IndustryRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, IndustryResponse>> getIndustries() async {
    try {
      final result = await remoteDataSource.getIndustries();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
