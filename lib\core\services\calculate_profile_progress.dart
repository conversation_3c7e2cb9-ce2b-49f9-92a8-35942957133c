import 'package:wesell/data/models/profile_marchant_model.dart';

class ProfileProgress {
  static int calculateProfileProgress(ProfileMarchantModel profileData) {
  int progress = 10; // default value

  // ✅ Add 20 if country is not null and not empty
  final country = profileData.attributes?.companyLocations?.country;
  if (country != null && country.trim().isNotEmpty) {
    progress += 20;
  }

  // ✅ Add 70 if both crNumber and crDocument are present
  final crNumber = profileData.company?.crNumber;
  final crDocument = profileData.company?.crDocument;
  if (crNumber != null && crDocument != null && crNumber.trim().isNotEmpty && crDocument.trim().isNotEmpty) {
    progress += 70;
  }

  // 🛡️ Ensure max cap at 100
  return progress.clamp(0, 100);
  }
}

