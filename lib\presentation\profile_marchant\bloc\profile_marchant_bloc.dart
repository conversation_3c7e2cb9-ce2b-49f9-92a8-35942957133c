import 'package:bloc/bloc.dart';
import '../../../domain/usecases/get_profile_usecase.dart';
import '../../../domain/usecases/upload_file_usecase.dart';
import '../../../domain/usecases/update_company_details_usecase.dart';
import '../../../domain/usecases/update_industry_details_usecase.dart';
import 'profile_marchant_event.dart';
import 'profile_marchant_state.dart';

class ProfileMarchantBloc extends Bloc<ProfileMarchantEvent, ProfileMarchantState> {
  final GetProfileUseCase getProfileUseCase;
  final UploadFileUseCase uploadFileUseCase;
  final UpdateCompanyDetailsUseCase updateCompanyDetailsUseCase;
  final UpdateIndustryDetailsUseCase updateIndustryDetailsUseCase;

  ProfileMarchantBloc({
    required this.getProfileUseCase,
    required this.uploadFileUseCase,
    required this.updateCompanyDetailsUseCase,
    required this.updateIndustryDetailsUseCase,
  }) : super(ProfileMarchantInitial()) {
    on<GetProfileEvent>(_onGetProfile);
    on<UploadImageEvent>(_onUploadImage);
    on<UpdateCompanyDetailsEvent>(_onUpdateCompanyDetails);
    on<UpdateIndustryDetailsEvent>(_onUpdateIndustryDetails);
  }

  Future<void> _onGetProfile(GetProfileEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(ProfileMarchantLoading());
    try {
      final result = await getProfileUseCase();
      result.fold(
        (failure) => emit(ProfileMarchantError(message: failure.message)),
        (profile) => emit(ProfileMarchantLoaded(profile: profile)),
      );
    } catch (e) {
      emit(ProfileMarchantError(message: e.toString()));
    }
  }

  Future<void> _onUploadImage(UploadImageEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(ImageUploadLoading());
    try {
      final result = await uploadFileUseCase(UploadFileParams(
        file: event.file,
        fileName: event.fileName,
      ));
      result.fold(
        (failure) => emit(ImageUploadError(message: failure.message)),
        (response) => emit(ImageUploadSuccess(
          fileId: response.fileId,
          fileName: response.fileName,
        )),
      );
    } catch (e) {
      emit(ImageUploadError(message: e.toString()));
    }
  }

  Future<void> _onUpdateCompanyDetails(UpdateCompanyDetailsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateCompanyDetailsLoading());
    try {
      final result = await updateCompanyDetailsUseCase(UpdateCompanyDetailsParams(
        companyName: event.companyName,
        logo: event.logo,
        description: event.description,
        detailedDescription: event.detailedDescription,
      ));
      result.fold(
        (failure) => emit(UpdateCompanyDetailsError(message: failure.message)),
        (response) => emit(UpdateCompanyDetailsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateCompanyDetailsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateIndustryDetails(UpdateIndustryDetailsEvent event, Emitter<ProfileMarchantState> emit) async {
    emit(UpdateIndustryDetailsLoading());
    try {
      final result = await updateIndustryDetailsUseCase(UpdateIndustryDetailsParams(
        industries: event.industries,
      ));
      result.fold(
        (failure) => emit(UpdateIndustryDetailsError(message: failure.message)),
        (response) => emit(UpdateIndustryDetailsSuccess(message: response.message)),
      );
    } catch (e) {
      emit(UpdateIndustryDetailsError(message: e.toString()));
    }
  }
}
