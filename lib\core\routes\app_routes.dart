import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/di/injection_container.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/auth/pages/terms_service_page.dart';
import 'package:wesell/presentation/auth/pages/merchant_profile_page.dart';
import 'package:wesell/presentation/auth/pages/user_role_page.dart';
import 'package:wesell/presentation/auth/pages/forgot_password_page.dart';
import 'package:wesell/presentation/profile_marchant/pages/company_details_page.dart';
import 'package:wesell/presentation/profile_marchant/pages/industry_details_page.dart';
import 'package:wesell/presentation/profile_marchant/pages/company_locations_page.dart';
import 'package:wesell/presentation/profile_marchant/pages/digital_information_page.dart';
import 'package:wesell/presentation/profile_marchant/pages/legal_documents_page.dart';
import 'package:wesell/presentation/profile_marchant/pages/profile_marchant.dart';
import 'package:wesell/presentation/tabs/pages/tabs_page.dart';
import '../../presentation/auth/pages/home_page.dart';
import '../../presentation/auth/pages/login_page.dart';
import '../../presentation/auth/pages/splash_page.dart';
import 'package:wesell/presentation/auth/pages/sign_up_page.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_bloc.dart';
import 'package:wesell/presentation/auth/bloc/login/login_bloc.dart';


class AppRoutes {
  // Route names
  static const String root = '/';
  static const String login = '/login';
  static const String forgotPassword = '/forgotPassword';
  static const String home = '/home';
  static const String userRole = '/userRole';
  static const String signup = '/signup';
  static const String merchantProfile = '/merchantProfile';
  static const String termsOfService = '/termsOfService';
  static const String tabs = '/tabs';
  static const String tokenTest = '/tokenTest';
  static const String disputes = '/disputes';
  static const String settings = '/settings';
  static const String profileMarchant = '/profileMarchant';
  static const String companyDetails = '/companyDetails';
  static const String industryDetails = '/industries-details';
  static const String companyLocations = '/company-locations';
  static const String digitalInformation = '/digital-informations';
  static const String legalDocuments = '/legal-documents';


  // Route generator
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case root:
      return MaterialPageRoute(
        builder: (_) => BlocProvider(
          create: (_) => serviceLocator<LoginBloc>(),
          child: const SplashPage(),
        ),
      );

      case login:
        return MaterialPageRoute(
          builder: (_) => const LoginPage(),
          settings: settings,
        );

      case forgotPassword:
        return MaterialPageRoute(
          builder: (_) => BlocProvider(
            create: (_) => serviceLocator<LoginBloc>(),
            child: const ForgotPasswordPage(),
          ),
          settings: settings,
        );

      case home:
        return MaterialPageRoute(
          builder: (_) => const HomePage(),
          settings: settings,
        );
        
      case userRole:
        return MaterialPageRoute(
          builder: (_) => const UserRolePage(),
          settings: settings,
        );

      case signup:
      final args = settings.arguments as Map<String, dynamic>?;
      final selectedRole = args != null && args['selectedRole'] != null ? args['selectedRole'] as String : '';
      return MaterialPageRoute(
        builder: (_) => BlocProvider(
          create: (_) => serviceLocator<SignupBloc>(),
          child: SignUpPage(selectedRole: selectedRole),
        ),
        settings: settings,
      );

      case merchantProfile:
      final args = settings.arguments as MerchantProfileArguments?;
      return MaterialPageRoute(
        builder: (_) => BlocProvider(
          create: (context) => serviceLocator<SignupBloc>(),
          child: MerchantProfilePage(args: args!),
        ),
        settings: settings,
      );

      case tabs:
        return MaterialPageRoute(
          builder: (_) => const TabsPage(),
          settings: settings,
        );

      case termsOfService:
        return MaterialPageRoute(
          builder: (_) => const TermsOfServicePage(),
          settings: settings,
        );

      case profileMarchant:
        return MaterialPageRoute(
          builder: (_) => const ProfileMarchant(),
          settings: settings,
        );

      case companyDetails:
      ProfileMarchantModel marchantProfileDetails = settings.arguments as ProfileMarchantModel;
        return MaterialPageRoute(
          builder: (_) =>  CompanyDetailsPage(marchantProfileDetails: marchantProfileDetails),
          settings: settings,
        );

      case industryDetails:
      ProfileMarchantModel profileData = settings.arguments as ProfileMarchantModel;
        return MaterialPageRoute(
          builder: (_) =>  IndustryDetailsPage(profileData: profileData),
          settings: settings,
        );

      case companyLocations:
      ProfileMarchantModel profileData = settings.arguments as ProfileMarchantModel;
        return MaterialPageRoute(
          builder: (_) =>  CompanyLocationsPage(profileData: profileData),
          settings: settings,
        );

      case digitalInformation:
      ProfileMarchantModel profileData = settings.arguments as ProfileMarchantModel;
        return MaterialPageRoute(
          builder: (_) =>  DigitalInformationPage(profileData: profileData),
          settings: settings,
        );

      case legalDocuments:
      ProfileMarchantModel profileData = settings.arguments as ProfileMarchantModel;
        return MaterialPageRoute(
          builder: (_) =>  LegalDocumentsPage(profileData: profileData),
          settings: settings,
        );

      default:
        return MaterialPageRoute(
          builder: (_) =>  Container(),
          settings: settings,
        );
    } 
  }

  // Navigation helpers
  static Future<void> pushScreen(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.of(context).pushNamed(routeName, arguments: arguments);
  }

  static Future<void> pushRoot(BuildContext context, String routeName) {
    return Navigator.of(context).pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
    );
  }

  static Future<void> pushHomeReplacement(BuildContext context,String routeName) {
    return Navigator.of(context).pushReplacementNamed(routeName);
  }
}