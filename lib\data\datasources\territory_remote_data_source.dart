import '../../core/constants/app_constants.dart';
import '../../core/network/dio_client.dart';
import '../models/territory_model.dart';

abstract class TerritoryRemoteDataSource {
  Future<TerritoryResponse> getCountries();
  Future<CityResponse> getCitiesByCountryId(String countryId);
}

class TerritoryRemoteDataSourceImpl implements TerritoryRemoteDataSource {
  final DioClient dioClient;

  TerritoryRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<TerritoryResponse> getCountries() async {
    final response = await dioClient.get(
      AppConstants.territoryCountryEndpoint,
    );
    return TerritoryResponse.fromJson(response.data as Map<String, dynamic>);
  }

  @override
  Future<CityResponse> getCitiesByCountryId(String countryId) async {
    final response = await dioClient.get(
      '${AppConstants.territoryCountryEndpoint}/$countryId/city',
    );
    return CityResponse.fromJson(response.data as Map<String, dynamic>);
  }
}
