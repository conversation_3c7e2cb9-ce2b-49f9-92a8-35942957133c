import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:wesell/main.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/preferences.dart';
import '../models/login_response.dart';
import '../datasources/auth_remote_data_source.dart';

class TokenRefreshService {
  final AuthRemoteDataSource remoteDataSource;
  bool _isRefreshing = false;

  TokenRefreshService({required this.remoteDataSource});

  Future<void> handleTokenRefresh(
    DioException error,
    ErrorInterceptorHandler handler,
    Dio dio,
  ) async {
    if (error.response?.statusCode == 401) {
      // Prevent infinite loop
      if (_isRefreshing) {
        return handler.reject(error);
      }

      try {
        _isRefreshing = true;

        final refreshToken = await Preferences.getRefreshToken();
        if (refreshToken == null) {
          _redirectToLogin();
          return handler.reject(error);
        }

        // Use a separate Dio instance for refresh token call to avoid interceptor
        await _refreshTokenWithSeparateDio(refreshToken);

        // Retry the original request with new token
        final requestOptions = error.requestOptions;
        final token = await Preferences.getAccessToken();
        requestOptions.headers['Authorization'] = 'Bearer $token';

        final retryResponse = await dio.fetch(requestOptions);
        return handler.resolve(retryResponse);
      } catch (e) {
        _redirectToLogin();
        return handler.reject(error);
      } finally {
        _isRefreshing = false;
      }
    }
    return handler.next(error);
  }

  Future<void> _refreshTokenWithSeparateDio(String refreshToken) async {
    // Create a separate Dio instance without interceptors to avoid infinite loop
    final separateDio = Dio();
    separateDio.options.baseUrl = AppConstants.baseUrl;
    separateDio.options.headers = {
      AppConstants.contentTypeHeader: AppConstants.applicationJson,
      AppConstants.acceptHeader: AppConstants.applicationJson,
    };
    separateDio.options.headers['Authorization'] = 'Bearer $refreshToken';

    final response = await separateDio.post(
      AppConstants.refreshTokenEndpoint,
      data: {'refreshToken': refreshToken},
    );
    debugPrint('ERROR[${response?.statusCode}] => PATH: ${response.requestOptions.path}');
    debugPrint('ERROR MESSAGE: ${response.data['error']?.message}');

    final loginResponse = LoginResponse.fromJson(response.data as Map<String, dynamic>);
    await _saveNewTokens(loginResponse);
  }

  Future<void> _redirectToLogin() async {
    await Preferences.clearTokens();
    navigatorKey.currentState?.pushNamedAndRemoveUntil('/login', (route) => false);
  }

  Future<void> _saveNewTokens(LoginResponse response) async {
    await Preferences.saveTokens(
      accessToken: response.accessToken,
      refreshToken: response.refreshToken,
    );
    await Preferences.saveUserFromToken(response.accessToken);
  }
}
