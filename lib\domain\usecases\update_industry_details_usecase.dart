import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateIndustryDetailsUseCase {
  final ProfileMarchantRepository repository;

  UpdateIndustryDetailsUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(UpdateIndustryDetailsParams params) async {
    return await repository.updateIndustryDetails(
      industries: params.industries,
    );
  }
}

class UpdateIndustryDetailsParams {
  final List<String> industries;

  UpdateIndustryDetailsParams({
    required this.industries,
  });
}
