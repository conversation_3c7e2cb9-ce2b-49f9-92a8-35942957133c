class IndustryResponse {
  final List<IndustryModel> rows;
  final int total;
  final int size;
  final int page;

  IndustryResponse({
    required this.rows,
    required this.total,
    required this.size,
    required this.page,
  });

  factory IndustryResponse.fromJson(Map<String, dynamic> json) {
    return IndustryResponse(
      rows: (json['rows'] as List<dynamic>)
          .map((item) => IndustryModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
      size: json['size'] as int,
      page: json['page'] as int,
    );
  }
}

class IndustryModel {
  final String industryName;
  final int merchantCount;
  final int dealmakerCount;
  final int jobsCount;
  final String industryId;
  final String createdAt;
  final String updatedAt;

  IndustryModel({
    required this.industryName,
    required this.merchantCount,
    required this.dealmakerCount,
    required this.jobsCount,
    required this.industryId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory IndustryModel.fromJson(Map<String, dynamic> json) {
    return IndustryModel(
      industryName: json['industryName'] as String,
      merchantCount: json['merchantCount'] as int,
      dealmakerCount: json['dealmakerCount'] as int,
      jobsCount: json['jobsCount'] as int,
      industryId: json['industryId'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );
  }
}
