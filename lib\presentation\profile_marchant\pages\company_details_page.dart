import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dotted_border.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';

class CompanyDetailsPage extends StatefulWidget {
  final CompanyDetails companyDetails;

  const CompanyDetailsPage({super.key, required this.companyDetails});

  @override
  _CompanyDetailsPageState createState() => _CompanyDetailsPageState();
}

class _CompanyDetailsPageState extends State<CompanyDetailsPage> {
  final _formKey = GlobalKey<FormState>();
  final _companyNameController = TextEditingController(text: '');
  final _briefController = TextEditingController();
  final _detailController = TextEditingController();
  String companyLogoId = '';


  final _briefMax = 500;
  final _detailMax = 1000;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppColors.whiteTheme,
        appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.companyDetails),
        leading: BackButton(color: AppColors.blackTextTheme),
      ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                  companyLogoId.isNotEmpty
                    ? Stack(
                      children: [
                        SizedBox(
                          height: 150,
                          width: 150,
                          child: Image.network(
                            '${AppConstants.mediaBaseUrl}$companyLogoId',
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(Icons.error);
                            }
                          ),
                        ),
                        Positioned(
                          top: 20,
                          right: 5,
                          child: Icon(Icons.camera_alt_outlined,color: AppColors.whiteTheme,))
                      ],
                    ):
                CustomDottedBorder(
                  borderRadius: 5,
                  borderColor: AppColors.iconLightTheme,
                  child: Container(
                    height: 120,
                     width: double.infinity,
                     alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.image_outlined, size: 40, color: Colors.black38),
                        const Text(AppStrings.uploadAFile, style:  TextStyle(color: AppColors.primaryTheme)),
                        const Text(AppStrings.fileTypeHint, style:  TextStyle(color: AppColors.textgreyTheme)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                CustomTextField(
                  controller: _companyNameController,
                  hintText: AppStrings.companyName,
                  validator: (value) => Validators.validateRequired(value, AppStrings.companyName),
                ),
                const SizedBox(height: 16),
                // Brief Description
                CustomTextField(
                  controller: _briefController,
                  hintText: AppStrings.briefCompanyDescription,
                  maxLines: 4,
                  maxLength: _briefMax,
                  validator: (value) => Validators.validateRequired(value, AppStrings.briefCompanyDescription),
                ),
                const SizedBox(height: 16),
                // Detailed Description
                CustomTextField(
                  controller: _detailController,
                  hintText: AppStrings.detailedCompanyDescription,
                  maxLines: 4,
                  maxLength: _detailMax,
                  validator: (value) => Validators.validateRequired(value, AppStrings.detailedCompanyDescription),
                ),
                const SizedBox(height: 20),
                // Save Button
                CustomButton(
                  width: double.infinity,
                  backgroundColor: AppColors.primaryTheme,
                  text: AppStrings.saveChanges,
                  onPressed:  _handleFormSubmit,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

   void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      //call bloc event here
    }
  }

  @override
  void initState() {
    _companyNameController.text = widget.companyDetails.companyName ?? '';
    _briefController.text = widget.companyDetails.description;
    _detailController.text = widget.companyDetails.detailedDescription;
    companyLogoId = widget.companyDetails.logo;
    super.initState();
  }
}



