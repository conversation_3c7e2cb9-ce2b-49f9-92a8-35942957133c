import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_constants.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/services/image_upload_service.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dotted_border.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class CompanyDetailsPage extends StatefulWidget {
  final ProfileMarchantModel marchantProfileDetails;

  const CompanyDetailsPage({super.key, required this.marchantProfileDetails });

  @override
  _CompanyDetailsPageState createState() => _CompanyDetailsPageState();
}

class _CompanyDetailsPageState extends State<CompanyDetailsPage> {
  final _formKey = GlobalKey<FormState>();
  final _companyNameController = TextEditingController(text: '');
  final _briefController = TextEditingController();
  final _detailController = TextEditingController();
  String companyLogoId = '';
  final _isUploading = ValueNotifier<bool>(false);

  final _briefMax = 500;
  final _detailMax = 1000;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is ImageUploadLoading) {
            _isUploading.value = true;
          } else if (state is ImageUploadSuccess) {
            _isUploading.value = false;
            companyLogoId = state.fileId;
            _showSnackBar('Image uploaded successfully');
          } else if (state is ImageUploadError) {
            _isUploading.value = false;
            _showSnackBar(state.message);
          } else if (state is UpdateCompanyDetailsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateCompanyDetailsError) {
            _showSnackBar(state.message);
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
          backgroundColor: AppColors.whiteTheme,
          title: Text(AppStrings.companyDetails),
          leading: BackButton(color: AppColors.blackTextTheme),
        ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ValueListenableBuilder<bool>(
                    valueListenable: _isUploading,
                    builder: (context, value, child) {
                      return value
                        ? Container(
                          height: 120,
                          width: double.infinity,
                          alignment: Alignment.center,
                          child: const CircularProgressIndicator(),
                        )
                      : companyLogoId.isNotEmpty
                        ? GestureDetector(
                            onTap: _pickAndUploadImage,
                            child: Stack(
                              children: [
                                SizedBox(
                                  height: 150,
                                  width: 150,
                                  child: Image.network(
                                    '${AppConstants.mediaBaseUrl}$companyLogoId',
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Icon(Icons.error);
                                    }
                                  ),
                                ),
                                Positioned(
                                  top: 20,
                                  right: 5,
                                  child: GestureDetector(
                                    onTap: _pickAndUploadImage,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.black54,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: const Icon(
                                        Icons.camera_alt_outlined,
                                        color: AppColors.whiteTheme,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : GestureDetector(
                            onTap: _pickAndUploadImage,
                            child: CustomDottedBorder(
                              borderRadius: 5,
                              borderColor: AppColors.iconLightTheme,
                              child: Container(
                                height: 120,
                                width: double.infinity,
                                alignment: Alignment.center,
                                child: const Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.image_outlined, size: 40, color: Colors.black38),
                                    Text(AppStrings.uploadAFile, style: TextStyle(color: AppColors.primaryTheme)),
                                    Text(AppStrings.fileTypeHint, style: TextStyle(color: AppColors.textgreyTheme)),
                                  ],
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                    
                const SizedBox(height: 20),
                CustomTextField(
                  controller: _companyNameController,
                  hintText: AppStrings.companyName,
                  validator: (value) => Validators.validateRequired(value, AppStrings.companyName),
                ),
                const SizedBox(height: 16),
                // Brief Description
                CustomTextField(
                  controller: _briefController,
                  hintText: AppStrings.briefCompanyDescription,
                  maxLines: 4,
                  maxLength: _briefMax,
                  validator: (value) => Validators.validateRequired(value, AppStrings.briefCompanyDescription),
                ),
                const SizedBox(height: 16),
                // Detailed Description
                CustomTextField(
                  controller: _detailController,
                  hintText: AppStrings.detailedCompanyDescription,
                  maxLines: 4,
                  maxLength: _detailMax,
                  validator: (value) => Validators.validateRequired(value, AppStrings.detailedCompanyDescription),
                ),
                const SizedBox(height: 20),
                // Save Button
                BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                  builder: (context,state) {
                    return CustomButton(
                      width: double.infinity,
                      backgroundColor: AppColors.primaryTheme,
                      text: AppStrings.saveChanges,
                      onPressed:   state is UpdateCompanyDetailsLoading ? null : _handleFormSubmit,
                      isLoading: state is UpdateCompanyDetailsLoading,
                    );
                  }
                )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

   void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateCompanyDetailsEvent(
        companyName: _companyNameController.text.trim(),
        logo: companyLogoId,
        description: _briefController.text.trim(),
        detailedDescription: _detailController.text.trim(),
      ));
    }
  }

    Future<void> _pickAndUploadImage() async {
    try {
      final file = await ImageUploadService.pickImageFromGallery();
      if (file != null && mounted) {
        final fileName = ImageUploadService.generateImageFileName(file.path.split('/').last);
        context.read<ProfileMarchantBloc>().add(UploadImageEvent(
          file: file,
          fileName: fileName,
          uploadId: companyLogoId,
        ));
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(e.toString());
      }
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  void initState() {
     _isUploading.value = true;
     _isUploading.value = false;
    _companyNameController.text = widget.marchantProfileDetails.companyName ?? '';
    if(widget.marchantProfileDetails.attributes?.companyDetails != null) {
      _briefController.text = widget.marchantProfileDetails.attributes!.companyDetails!.description ?? '';
      _detailController.text = widget.marchantProfileDetails.attributes!.companyDetails!.detailedDescription ?? '';
      companyLogoId = widget.marchantProfileDetails.attributes!.companyDetails!.logo ?? '';
      print(companyLogoId);
    }
    super.initState();
  }
}



