import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/user_model.dart';
import '../../data/models/login_response.dart';
import '../../data/models/register_response.dart';

abstract class AuthRepository {
  Future<Either<Failure, LoginResponse>> login({
    required String email,
    required String password,
  });
  
  Future<Either<Failure, RegisterResponse>> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String type,
    // Merchant fields (optional)
    String? companyLegalName,
    String? crNumber,
    int? investedCapital,
    String? investedCapitalUnit,
    String? companySize,
    String? licenseDocument,
    String? crDocument,
  });
  
  Future<Either<Failure, LoginResponse>> refreshToken({
    required String refreshToken,
  });
  
  Future<Either<Failure, void>> logout();

  Future<Either<Failure, User?>> getCurrentUser();

  Future<Either<Failure, bool>> isLoggedIn();

  Future<Either<Failure, String>> forgotPassword({
    required String email,
  });
}
