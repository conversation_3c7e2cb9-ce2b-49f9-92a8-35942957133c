class TerritoryResponse {
  final List<CountryModel> rows;
  final int total;
  final int size;
  final int page;

  TerritoryResponse({
    required this.rows,
    required this.total,
    required this.size,
    required this.page,
  });

  factory TerritoryResponse.fromJson(Map<String, dynamic> json) {
    return TerritoryResponse(
      rows: (json['rows'] as List<dynamic>)
          .map((item) => CountryModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
      size: json['size'] as int,
      page: json['page'] as int,
    );
  }
}

class CountryModel {
  final String countryName;
  final bool status;
  final String countryId;
  final List<CityModel> cities;

  CountryModel({
    required this.countryName,
    required this.status,
    required this.countryId,
    required this.cities,
  });

  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      countryName: json['countryName'] as String,
      status: json['status'] as bool,
      countryId: json['countryId'] as String,
      cities: (json['cities'] as List<dynamic>?)
              ?.map((item) => CityModel.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

class CityModel {
  final String cityName;
  final String countryId;
  final bool status;
  final String cityId;

  CityModel({
    required this.cityName,
    required this.countryId,
    required this.status,
    required this.cityId,
  });

  factory CityModel.fromJson(Map<String, dynamic> json) {
    return CityModel(
      cityName: json['cityName'] as String,
      countryId: json['countryId'] as String,
      status: json['status'] as bool,
      cityId: json['cityId'] as String,
    );
  }
}

class CityResponse {
  final List<CityModel> rows;
  final int total;
  final int size;
  final int page;

  CityResponse({
    required this.rows,
    required this.total,
    required this.size,
    required this.page,
  });

  factory CityResponse.fromJson(Map<String, dynamic> json) {
    return CityResponse(
      rows: (json['rows'] as List<dynamic>)
          .map((item) => CityModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
      size: json['size'] as int,
      page: json['page'] as int,
    );
  }
}
