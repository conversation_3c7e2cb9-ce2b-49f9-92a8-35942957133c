import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/repositories/profile_marchant_repository.dart';
import '../datasources/profile_marchant_remote_data_source.dart';
import '../models/profile_marchant_model.dart';
import '../models/update_company_details_response.dart';

class ProfileMarchantRepositoryImpl implements ProfileMarchantRepository {
  final ProfileMarchantRemoteDataSource remoteDataSource;

  ProfileMarchantRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, ProfileMarchantModel>> getProfile() async {
    try {
      final response = await remoteDataSource.getProfile();
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  }) async {
    try {
      final response = await remoteDataSource.updateCompanyDetails(
        companyName: companyName,
        logo: logo,
        description: description,
        detailedDescription: detailedDescription,
      );
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateIndustryDetails({
    required List<String> industries,
  }) async {
    try {
      final response = await remoteDataSource.updateIndustryDetails(
        industries: industries,
      );
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateCompanyLocations({
    required String country,
    required String city,
    required String address,
    required String poBox,
  }) async {
    try {
      final response = await remoteDataSource.updateCompanyLocations(
        country: country,
        city: city,
        address: address,
        poBox: poBox,
      );
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateDigitalInformation({
    required String website,
    required String facebook,
    required String twitter,
    required String linkedin,
    required String youtube,
    required String tiktok,
    required String snapchat,
  }) async {
    try {
      final response = await remoteDataSource.updateDigitalInformation(
        website: website,
        facebook: facebook,
        twitter: twitter,
        linkedin: linkedin,
        youtube: youtube,
        tiktok: tiktok,
        snapchat: snapchat,
      );
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } on TimeoutException catch (e) {
      return Left(TimeoutFailure(e.message));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
