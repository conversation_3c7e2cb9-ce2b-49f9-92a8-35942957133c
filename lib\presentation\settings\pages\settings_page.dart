import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/routes/app_routes.dart';
import 'package:wesell/core/utils/preferences.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late final List<MenuItem> menuItems;

  @override
  Widget build(BuildContext context) {
     return Scaffold(
      backgroundColor: AppColors.backgroundTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.settings),
        elevation: 0,
      ),
      body: ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final item = menuItems[index];
        return ListTile(
          title: Text(
            item.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.blackTextTheme,
            ),
          ),
          trailing: const Icon(Icons.chevron_right, color: Colors.black54),
          onTap: item.onTap,
          contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
        );
      },
    ),
    );
  }

  
  @override
  void initState() {
    super.initState();
    menuItems = [
      MenuItem(title: AppStrings.profile, onTap: profile),
      MenuItem(title: AppStrings.disputes),
      MenuItem(title: AppStrings.settings),
      MenuItem(title: AppStrings.logout, onTap: logout),
    ];
  }

  void logout(){
    Preferences.clearTokens();
    AppRoutes.pushRoot(context, AppRoutes.login);
  }

  void profile(){
    AppRoutes.pushScreen(context, AppRoutes.profileMarchant);
  }
}

class MenuItem {
  final String title;
  final VoidCallback? onTap;

  MenuItem({required this.title, this.onTap});
}