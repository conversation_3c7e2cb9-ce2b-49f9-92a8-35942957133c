import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/core/utils/validators.dart';
import 'package:wesell/data/models/profile_marchant_model.dart';
import 'package:wesell/data/models/territory_model.dart';
import 'package:wesell/presentation/widgets/custom_button.dart';
import 'package:wesell/presentation/widgets/custom_dropdown_form_field.dart';
import 'package:wesell/presentation/widgets/custom_text_field.dart';
import '../bloc/profile_marchant_bloc.dart';
import '../bloc/profile_marchant_event.dart';
import '../bloc/profile_marchant_state.dart';

class CompanyLocationsPage extends StatefulWidget {
  final ProfileMarchantModel profileData;

  const CompanyLocationsPage({super.key, required this.profileData});

  @override
  _CompanyLocationsPageState createState() => _CompanyLocationsPageState();
}

class _CompanyLocationsPageState extends State<CompanyLocationsPage> {
  final _formKey = GlobalKey<FormState>();
  final _addressController = TextEditingController();
  final _poBoxController = TextEditingController();

  CountryModel? _selectedCountry;
  CityModel? _selectedCity;

  List<CountryModel> _countries = [];
  List<CityModel> _cities = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
    // Load countries when page initializes
    context.read<ProfileMarchantBloc>().add(GetCountriesEvent());
  }

  void _initializeData() {
    final locations = widget.profileData.attributes?.companyLocations;
    if (locations != null) {
      _addressController.text = locations.address ?? '';
      _poBoxController.text = locations.poBox ?? '';
      // Note: We'll set selected country and city after countries are loaded
    }
  }

  void _onCountryChanged(CountryModel? country) {
    setState(() {
      _selectedCountry = country;
      _selectedCity = null; // Reset city when country changes
      _cities = []; // Clear cities list
    });

    // Load cities for the selected country
    if (country != null) {
      context.read<ProfileMarchantBloc>().add(GetCitiesEvent(countryId: country.countryId));
    }
  }

  void _onCityChanged(CityModel? city) {
    setState(() {
      _selectedCity = city;
    });
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _handleFormSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<ProfileMarchantBloc>().add(UpdateCompanyLocationsEvent(
        country: _selectedCountry?.countryName ?? '',
        city: _selectedCity?.cityName ?? '',
        address: _addressController.text.trim(),
        poBox: _poBoxController.text.trim(),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: BlocListener<ProfileMarchantBloc, ProfileMarchantState>(
        listener: (context, state) {
          if (state is UpdateCompanyLocationsSuccess) {
            _showSnackBar(state.message);
            // Refresh profile data after successful update
            context.read<ProfileMarchantBloc>().add(GetProfileEvent());
          } else if (state is UpdateCompanyLocationsError) {
            _showSnackBar(state.message);
          } else if (state is CountriesLoaded) {
            setState(() {
              _countries = state.countries;
              // Try to find and set the previously selected country
              final locations = widget.profileData.attributes?.companyLocations;
              if (locations?.country != null) {
                _selectedCountry = _countries.firstWhere(
                  (country) => country.countryName == locations!.country,
                  orElse: () => _countries.first,
                );
                // Load cities for the selected country
                if (_selectedCountry != null) {
                  context.read<ProfileMarchantBloc>().add(GetCitiesEvent(countryId: _selectedCountry!.countryId));
                }
              }
            });
          } else if (state is CitiesLoaded) {
            setState(() {
              _cities = state.cities;
              // Try to find and set the previously selected city
              final locations = widget.profileData.attributes?.companyLocations;
              if (locations?.city != null) {
                _selectedCity = _cities.firstWhere(
                  (city) => city.cityName == locations!.city,
                  orElse: () => _cities.first,
                );
              }
            });
          } else if (state is CountriesError) {
            _showSnackBar('Error loading countries: ${state.message}');
          } else if (state is CitiesError) {
            _showSnackBar('Error loading cities: ${state.message}');
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.whiteTheme,
          appBar: AppBar(
            backgroundColor: AppColors.whiteTheme,
            title: const Text(AppStrings.companyLocation),
            leading: const BackButton(color: AppColors.blackTextTheme),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Country Dropdown
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      final isLoading = state is CountriesLoading;
                      return CustomDropdownFormField<CountryModel>(
                        value: _selectedCountry,
                        items: _countries,
                        hintText: AppStrings.country,
                        itemLabel: (item) => item.countryName,
                        onChanged: isLoading ? null : _onCountryChanged,
                        validator: (value) => value == null ? 'Please select a country' : null,
                        disabled: isLoading,
                      );
                    },
                  ),
                  const SizedBox(height: 16),

                  // City Dropdown
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      final isLoading = state is CitiesLoading;
                      return CustomDropdownFormField<CityModel>(
                        value: _selectedCity,
                        items: _cities,
                        hintText: AppStrings.city,
                        itemLabel: (item) => item.cityName,
                        onChanged: isLoading ? null : _onCityChanged,
                        validator: (value) => value == null ? 'Please select a city' : null,
                        disabled: isLoading,
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Address Field
                  CustomTextField(
                    controller: _addressController,
                    labelText: AppStrings.address,
                    hintText: AppStrings.address,
                    validator: (value) => Validators.validateRequired(value, AppStrings.address),
                  ),
                  const SizedBox(height: 16),
                  
                  // PO Box Field
                  CustomTextField(
                    controller: _poBoxController,
                    labelText: AppStrings.poBox,
                    hintText: AppStrings.poBox,
                    validator: (value) => Validators.validateRequired(value, AppStrings.poBox),
                  ),
                  const SizedBox(height: 24),
                  
                  // Save Button
                  BlocBuilder<ProfileMarchantBloc, ProfileMarchantState>(
                    builder: (context, state) {
                      return CustomButton(
                        width: double.infinity,
                        backgroundColor: AppColors.primaryTheme,
                        text: AppStrings.saveChanges,
                        onPressed: state is UpdateCompanyLocationsLoading ? null : _handleFormSubmit,
                        isLoading: state is UpdateCompanyLocationsLoading,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    _poBoxController.dispose();
    super.dispose();
  }
}
