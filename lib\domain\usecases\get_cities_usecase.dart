import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/territory_model.dart';
import '../repositories/territory_repository.dart';

class GetCitiesUseCase {
  final TerritoryRepository repository;

  GetCitiesUseCase(this.repository);

  Future<Either<Failure, CityResponse>> call(String countryId) async {
    return await repository.getCitiesByCountryId(countryId);
  }
}
