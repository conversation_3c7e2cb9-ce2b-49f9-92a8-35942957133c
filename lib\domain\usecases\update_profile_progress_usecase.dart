import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/update_company_details_response.dart';
import '../repositories/profile_marchant_repository.dart';

class UpdateProfileProgressUseCase {
  final ProfileMarchantRepository repository;

  UpdateProfileProgressUseCase(this.repository);

  Future<Either<Failure, UpdateCompanyDetailsResponse>> call(double profileProgress) async {
    return await repository.updateProfileProgress(profileProgress: profileProgress);
  }
}
